import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import {
  Box,
  FormControl,
  HStack,
  Input,
  ScrollView,
  StatusBar,
  Text,
  VStack,
} from "native-base";
import React, { useEffect, useState } from "react";
import { SafeAreaView, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";
import LoadingModal from "../../components/Loading/LoadingModal";
import Puk from "../../services/SelfCare/Puk";
import LocalStore from "../../utilities/store";
import * as Clipboard from "expo-clipboard";

const RequestPuk = ({ route }) => {
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();
  const [nationalId, setNationalID] = useState("");
  const [nationalIdError, setNationalIDError] = useState("");
  const [lastName, setLastName] = useState("");
  const [errorLastName, setErrorLastName] = useState("");
  const [details, setDetails] = useState([]);
  const [Phone, setPhone] = useState(null);

  useEffect(() => {
    const getPhone = async () => {
      const Phone = await LocalStore.getData("@username");
      //check if phone number is stored
      if (Phone !== null) {
        setPhone(Phone);
      }
    };
    getPhone();
  }, []);

  const validate = () => {
    if (!lastName) {
      setErrorLastName("This field is required");
      return false;
    }
    if (!/^[a-zA-Z]+$/.test(lastName)) {
      setErrorLastName("Last name must contain letters only");
      return false;
    }
    //check national ID
    if (!nationalId) {
      setNationalIDError("This field is required");
      return false;
    }

    if (!idField) {
      setNationalIDError(
        "National ID field is invalid, check for space/s between letters or and the end or make sure its a correct zimbabwean ID."
      );
      return false;
    }

    return true;
  };

  const getDetails = async () => {
    try {
      setLoading(true);
      setFailed(false);
      const response = await Puk.getDetails("263" + Phone);

      if (response.data.success) {
        setLoading(false);
        setDetails([response.data.body]);
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const handleInputChange = (text) => {
    const regex = /^([0-9][0-9])-([0-9]{6}|[0-9]{7})-([a-zA-Z])([0-9]{2})$/;
    setIdField(regex.test(text));
    setNationalID(text);
  };

  const copyToClipboard = async (text) => {
    await Clipboard.setStringAsync(text);
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        <LoadingModal isLoading={loading} />

        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <HStack space={moderateScale(16)} alignItems={"center"}>
              <Box
                bg={"#F68C1E"}
                rounded={"full"}
                w={moderateScale(48)}
                h={moderateScale(48)}
                alignItems={"center"}
                justifyContent={"center"}>
                <MaterialCommunityIcons
                  name="arrow-vertical-lock"
                  size={moderateScale(24)}
                  color="white"
                />
              </Box>
              <VStack flex={1}>
                <Text
                  fontFamily={"openSansBold"}
                  fontSize={moderateScale(28)}
                  color={"gray.900"}>
                  Request PUK
                </Text>
                <Text
                  fontFamily={"openSansMedium"}
                  fontSize={moderateScale(16)}
                  color={"gray.500"}>
                  Get your PUK codes to unlock your SIM
                </Text>
              </VStack>
            </HStack>
          </VStack>
        </Box>

        {/* Content */}
        <Box px={moderateScale(20)} py={moderateScale(20)}>
          <VStack space={moderateScale(20)}>
            {/* Success/Error Messages */}
            {success && (
              <Box
                bg={"green.50"}
                rounded={"xl"}
                p={moderateScale(16)}
                borderWidth={1}
                borderColor={"green.200"}>
                <HStack space={moderateScale(12)} alignItems={"center"}>
                  <MaterialCommunityIcons
                    name="check-circle"
                    size={moderateScale(20)}
                    color="#10B981"
                  />
                  <Text
                    fontFamily={"openSansMedium"}
                    fontSize={moderateScale(14)}
                    color={"green.700"}>
                    {successMessage}
                  </Text>
                </HStack>
              </Box>
            )}

            {failed && (
              <Box
                bg={"red.50"}
                rounded={"xl"}
                p={moderateScale(16)}
                borderWidth={1}
                borderColor={"red.200"}>
                <HStack space={moderateScale(12)} alignItems={"center"}>
                  <MaterialCommunityIcons
                    name="alert-circle"
                    size={moderateScale(20)}
                    color="#EF4444"
                  />
                  <Text
                    fontFamily={"openSansMedium"}
                    fontSize={moderateScale(14)}
                    color={"red.700"}>
                    {errorMessage}
                  </Text>
                </HStack>
              </Box>
            )}

            {/* Get PUK Button */}
            <TouchableOpacity onPress={getDetails} disabled={loading}>
              <Box
                bg={loading ? "gray.300" : "#F68C1E"}
                rounded={"xl"}
                py={moderateScale(16)}
                alignItems={"center"}
                shadow={loading ? 0 : 2}>
                <HStack space={moderateScale(8)} alignItems={"center"}>
                  {loading && (
                    <MaterialCommunityIcons
                      name="loading"
                      size={moderateScale(20)}
                      color="white"
                    />
                  )}
                  <Text
                    fontFamily={"openSansBold"}
                    fontSize={moderateScale(16)}
                    color={"white"}>
                    {loading ? "Getting PUK..." : "Get PUK Details"}
                  </Text>
                </HStack>
              </Box>
            </TouchableOpacity>

            {/* PUK Details */}
            {details && details.length > 0 && (
              <VStack space={moderateScale(16)}>
                <Text
                  fontFamily={"openSansBold"}
                  fontSize={moderateScale(18)}
                  color={"gray.900"}
                  textAlign={"center"}>
                  Your PUK Details
                </Text>

                {details.map((item, index) => (
                  <Box
                    key={index}
                    bg={"white"}
                    rounded={"2xl"}
                    p={moderateScale(20)}
                    shadow={2}
                    borderWidth={1}
                    borderColor={"gray.100"}>
                    <VStack space={moderateScale(16)}>
                      {/* PUK1 */}
                      <HStack
                        justifyContent={"space-between"}
                        alignItems={"center"}>
                        <VStack flex={1}>
                          <Text
                            fontFamily={"openSansBold"}
                            fontSize={moderateScale(14)}
                            color={"gray.500"}>
                            PUK1
                          </Text>
                          <Text
                            fontFamily={"openSansBold"}
                            fontSize={moderateScale(18)}
                            color={"gray.900"}>
                            {item.puk1}
                          </Text>
                        </VStack>
                        <TouchableOpacity
                          onPress={() => copyToClipboard(item.puk1)}>
                          <Box
                            bg={"gray.100"}
                            rounded={"full"}
                            w={moderateScale(40)}
                            h={moderateScale(40)}
                            alignItems={"center"}
                            justifyContent={"center"}>
                            <MaterialCommunityIcons
                              name="content-copy"
                              size={moderateScale(20)}
                              color="#F68C1E"
                            />
                          </Box>
                        </TouchableOpacity>
                      </HStack>

                      {/* Divider */}
                      <Box h={1} bg={"gray.200"} />

                      {/* PUK2 */}
                      <HStack
                        justifyContent={"space-between"}
                        alignItems={"center"}>
                        <VStack flex={1}>
                          <Text
                            fontFamily={"openSansBold"}
                            fontSize={moderateScale(14)}
                            color={"gray.500"}>
                            PUK2
                          </Text>
                          <Text
                            fontFamily={"openSansBold"}
                            fontSize={moderateScale(18)}
                            color={"gray.900"}>
                            {item.puk2}
                          </Text>
                        </VStack>
                        <TouchableOpacity
                          onPress={() => copyToClipboard(item.puk2)}>
                          <Box
                            bg={"gray.100"}
                            rounded={"full"}
                            w={moderateScale(40)}
                            h={moderateScale(40)}
                            alignItems={"center"}
                            justifyContent={"center"}>
                            <MaterialCommunityIcons
                              name="content-copy"
                              size={moderateScale(20)}
                              color="#F68C1E"
                            />
                          </Box>
                        </TouchableOpacity>
                      </HStack>
                    </VStack>
                  </Box>
                ))}
              </VStack>
            )}

            {/* No Details Message */}
            {details && details.length === 0 && !loading && (
              <Box
                bg={"white"}
                rounded={"2xl"}
                p={moderateScale(40)}
                alignItems={"center"}
                shadow={1}>
                <MaterialCommunityIcons
                  name="lock-alert"
                  size={moderateScale(48)}
                  color="#9CA3AF"
                />
                <Text
                  fontFamily={"openSansBold"}
                  fontSize={moderateScale(16)}
                  color={"gray.500"}
                  mt={moderateScale(16)}
                  textAlign={"center"}>
                  No PUK Details Available
                </Text>
                <Text
                  fontFamily={"openSansMedium"}
                  fontSize={moderateScale(14)}
                  color={"gray.400"}
                  mt={moderateScale(8)}
                  textAlign={"center"}>
                  Please try again or contact customer service
                </Text>
              </Box>
            )}
          </VStack>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default RequestPuk;
