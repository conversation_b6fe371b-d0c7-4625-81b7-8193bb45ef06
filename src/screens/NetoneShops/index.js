import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Box,
  HStack,
  Input,
  ScrollView,
  StatusBar,
  Text,
  VStack,
} from "native-base";
import React, { useEffect, useState } from "react";
import { SafeAreaView, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";
import LocalStore from "../../utilities/store";

const Shops = ({ route }) => {
  const navigation = useNavigation();
  const [profile, setProfile] = useState(null);
  useEffect(() => {
    const getProfile = async () => {
      const profile = await LocalStore.getData("@userProfile");

      if (profile !== null) {
        setProfile(profile);
      }
    };
    getProfile();
  }, []);
  const data = [
    {
      Column1: "Machipisa",
      Address: "Stand  No.985 Machipisa, Highfields, Harare",
      Latitude: -17.885574,
      Longitude: 30.982561,
      Zone: 1,
    },
    {
      Column1: "Chiredzi",
      Address: "Shop Number, Old Mutual Complex, Chiredzi",
      Latitude: -21.044631,
      Longitude: 31.669666,
      Zone: 5,
    },
    {
      Column1: "Karoi",
      Address: "No 1 Roseway Street Petrotrade Complex Karoi",
      Latitude: -16.819875,
      Longitude: 29.687256,
      Zone: 4,
    },
    {
      Column1: "Kwekwe",
      Address: "Shop Number 1, CAIPF Building, Kwekwe",
      Latitude: -18.929146,
      Longitude: 29.807421,
      Zone: 5,
    },
    {
      Column1: "Marondera",
      Address: "1137 Second St, CAIPF Building,  Marondera",
      Latitude: -18.192057,
      Longitude: 31.544495,
      Zone: 3,
    },
    {
      Column1: "Chipinge",
      Address: "Shop FF6 NSSA Complex Chipinge",
      Latitude: -20.191957,
      Longitude: 32.619516,
    },
    {
      Column1: "Kadoma",
      Address: "Shop M4 Sam Levy Complex Kadoma",
      Latitude: -18.337996,
      Longitude: 29.913446,
      Zone: 4,
    },
    {
      Column1: "Nyanga",
      Address: "02 Shonalanga Nyanga",
      Latitude: -18.232736,
      Longitude: 32.738425,
      Zone: 3,
    },
    {
      Column1: "Rusape",
      Address: "Cnr 13 R Mugabe and H Chitepo Rusape",
      Latitude: -18.532817,
      Longitude: 32.124845,
      Zone: 3,
    },
    {
      Column1: "Zvishavane",
      Address: "1895 R Mugabe Zvishavane",
      Latitude: -20.317373,
      Longitude: 30.054696,
      Zone: 5,
    },
    {
      Column1: "Chegutu",
      Address: "15 Queens Street Chegutu ",
      Latitude: -18.134329,
      Longitude: 30.145975,
      Zone: 4,
    },
    {
      Column1: "Ngezi",
      Address: "95 A MANA COMPLEX NGEZI",
      Latitude: -18.676805,
      Longitude: 30.277845,
    },
    {
      Column1: "Mount Darwin",
      Address: "Number 1, Main St, former FMC Complex, Darwin",
      Latitude: -16.776632,
      Longitude: 31.576467,
      Zone: 4,
    },
    {
      Column1: "Chinhoyi",
      Address: "38B Magamba way Chinhoyi",
      Latitude: -17.359015,
      Longitude: 30.197532,
    },
    {
      Column1: "Murombedzi",
      Address: "stand number 113 Murombedzi",
      Latitude: -17.707498,
      Longitude: 30.200477,
    },
    {
      Column1: "Airport shop",
      Address: "Airport",
      Latitude: -17.918279,
      Longitude: false,
    },
    {
      Column1: "Kariba",
      Address: "SHOP 5 TEE BOX COMPLEX NYAMHUNGA 2  KARIBA ",
      Latitude: -16.517669,
      Longitude: 28.848863,
    },
    {
      Column1: "Longcheng",
      Address: "79/80 Longcheng Complex, Belvedere, Harare",
      Latitude: -17.826585,
      Longitude: 31.003291,
      Zone: 1,
    },
    {
      Column1: "Bindura",
      Address: "Shop 16 NSSA Complex Bindura",
      Latitude: -17.311066,
      Longitude: 31.337641,
    },
    {
      Column1: "Mvurwi",
      Address: "31 Birmingham Road Mvurwi",
      Latitude: -17.03278,
      Longitude: 30.855446,
      Zone: 4,
    },
    {
      Column1: "Harare ShowGrounds",
      Address: "Harare ShowGrounds",
    },
    {
      Column1: "Chimanimani",
      Address: "Stand 263 Zimpost Chimanimani Post Office",
      Latitude: -19.813605,
      Longitude: 32.862725,
    },
    {
      Column1: "Vic falls",
      Address: "shop 21 sawanga mall victoria falls",
      Latitude: -17.930335,
      Longitude: 25.83485,
    },
    {
      Column1: "Eastgate",
      Address: "Eastgate",
      Latitude: -17.83194,
      Longitude: 31.052269,
    },
    {
      Column1: "Kamfinsa",
      Address: "307 Kamfinsa , Greendale Shops, Harare. ",
      Latitude: -17.805579,
      Longitude: 31.12578,
      Zone: 1,
    },

    {
      Column1: "Gwanda Shop",
      Address: "18 NSSA COMPLEX GWANDA",
      Latitude: -20.9440347,
      Longitude: 29.0072282,
      Zone: 2,
    },
    {
      Column1: "Borrowdale shop",
      Address: "Sam Levy's Village SHOP No.28,10 BORROWDALE HARARE",
      Zone: 1,
    },
    {
      Column1: "Mpandawana",
      Address: "Stand No. 385 Zimpost Mpandawana Gutu",
    },
    {
      Column1: "Checheche Zimpost",
      Address: "1020 zimpost Checheche",
      Latitude: -20.761003,
      Longitude: 32.226196,
      Zone: 4,
    },
    {
      Column1: "Shamva",
      Address: "Zimpost Shamva",
      Latitude: -17.298,
      Longitude: 31.5653,
    },
    {
      Column1: "Herbert Chitepo Shop",
      Address: "344 Herbert Chitepo Ave",
      Latitude: -17.8178799,
      Longitude: 31.0629675,
      Zone: 1,
    },
    {
      Column1: "Makoni",
      Address: "Shop no 9 Phillip Munosi Complex Makoni, Chitungwiza",
      Latitude: -18.013076,
      Longitude: 31.103154,
      Zone: 1,
    },
    {
      Column1: "hwange",
      Address: "1 corronation drive hwange",
    },
    {
      Column1: "Vic falls Airport",
      Address: "victoria falls airport",
    },
    {
      Column1: "binga",
      Address: "binga zimpost",
    },
    {
      Column1: "lupane",
      Address: "lupane zimpost",
    },
    {
      Column1: "Mataga",
      Address: "80 Mabhena Complex Mataga",
      Latitude: -20.846341,
      Longitude: 30.194768,
      Zone: 5,
    },
    {
      Column1: "Murambinda",
      Address: "3811 Murambinda Zimpost",
      Latitude: -19.268522,
      Longitude: 31.652995,
    },
    {
      Column1: "Gweru",
      Address: "Corner 7th and R G Mugabe way,CAIPF Building Gweru",
    },
    {
      Column1: "Beitbridge",
      Address: "Shop no 1 zesa complex Beitbridge",
    },

    {
      Column1: "Mutare",
      Address: "93-94 Herbet Chitepo Street, Fidelity Building Mutare",
      Latitude: -18.9733456,
      Longitude: 32.6710023,
      Zone: 3,
    },
    {
      Column1: "Westgate",
      Address: "Shop number 6 Pavilion Westgate",
    },
    {
      Column1: "Magunje",
      Address: "Magunje Zimpost",
      Zone: 4,
    },
    {
      Column1: "Shamva",
      Address: "Shamva Zimpost",
      Zone: 4,
    },
    {
      Column1: "Sanyati.",
      Address: "Sanyati Zimpost",
      Latitude: -17.9869533,
      Longitude: 29.2429117,
      Zone: 4,
    },
  ];

  const [searchTerm, setSearchTerm] = useState("");

  const filteredData = data.filter(
    (item) =>
      item.Column1.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.Address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <Text
              fontFamily={"openSansBold"}
              fontSize={moderateScale(28)}
              color={"gray.900"}>
              NetOne Shops
            </Text>
            <Text
              fontFamily={"openSansMedium"}
              fontSize={moderateScale(16)}
              color={"gray.500"}>
              Find our stores near you
            </Text>
          </VStack>
        </Box>

        {/* Search Bar */}
        <Box px={moderateScale(20)} py={moderateScale(16)}>
          <HStack
            bg={"white"}
            rounded={"xl"}
            p={moderateScale(12)}
            shadow={1}
            borderWidth={1}
            borderColor={"gray.100"}
            alignItems={"center"}
            space={moderateScale(12)}>
            <MaterialCommunityIcons
              name="magnify"
              size={moderateScale(20)}
              color="#9CA3AF"
            />
            <Input
              flex={1}
              fontFamily={"openSansMedium"}
              placeholder="Search by location or address"
              fontSize={moderateScale(14)}
              borderWidth={0}
              bg={"transparent"}
              onChangeText={(text) => setSearchTerm(text)}
              value={searchTerm}
              _focus={{
                borderWidth: 0,
                bg: "transparent",
              }}
            />
          </HStack>
        </Box>

        {/* Shops List */}
        <Box px={moderateScale(20)} pb={moderateScale(20)}>
          <VStack space={moderateScale(12)}>
            {filteredData.map((item, index) => (
              <Box
                key={index}
                bg={"white"}
                rounded={"2xl"}
                p={moderateScale(20)}
                shadow={2}
                borderWidth={1}
                borderColor={"gray.100"}>
                <HStack space={moderateScale(16)} alignItems={"flex-start"}>
                  <Box
                    bg={"#F68C1E"}
                    rounded={"full"}
                    w={moderateScale(48)}
                    h={moderateScale(48)}
                    alignItems={"center"}
                    justifyContent={"center"}>
                    <MaterialCommunityIcons
                      name="store"
                      size={moderateScale(24)}
                      color="white"
                    />
                  </Box>
                  <VStack flex={1} space={moderateScale(8)}>
                    <Text
                      fontFamily={"openSansBold"}
                      fontSize={moderateScale(16)}
                      color={"gray.900"}>
                      {item.Column1}
                    </Text>
                    <Text
                      fontFamily={"openSansMedium"}
                      fontSize={moderateScale(14)}
                      color={"gray.600"}
                      lineHeight={moderateScale(20)}>
                      {item.Address}
                    </Text>
                    {item.Zone && (
                      <Box
                        bg={"gray.100"}
                        rounded={"full"}
                        px={moderateScale(12)}
                        py={moderateScale(4)}
                        alignSelf={"flex-start"}>
                        <Text
                          fontFamily={"openSansSemiBold"}
                          fontSize={moderateScale(12)}
                          color={"gray.700"}>
                          Zone {item.Zone}
                        </Text>
                      </Box>
                    )}
                  </VStack>
                </HStack>
              </Box>
            ))}

            {filteredData.length === 0 && (
              <Box
                bg={"white"}
                rounded={"2xl"}
                p={moderateScale(40)}
                alignItems={"center"}
                shadow={1}>
                <MaterialCommunityIcons
                  name="store-search"
                  size={moderateScale(48)}
                  color="#9CA3AF"
                />
                <Text
                  fontFamily={"openSansBold"}
                  fontSize={moderateScale(16)}
                  color={"gray.500"}
                  mt={moderateScale(16)}
                  textAlign={"center"}>
                  No shops found
                </Text>
                <Text
                  fontFamily={"openSansMedium"}
                  fontSize={moderateScale(14)}
                  color={"gray.400"}
                  mt={moderateScale(8)}
                  textAlign={"center"}>
                  Try searching with a different location
                </Text>
              </Box>
            )}
          </VStack>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Shops;
